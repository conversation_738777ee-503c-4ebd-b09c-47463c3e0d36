<!--
 * @Description: 签到功能页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 最近30天日历 + 连签统计 + 签到按钮 + 签到记录
-->
<template>
  <view class="sign-page">
    <!-- 连签统计卡片 -->
    <view class="sign-status-card">
      <view class="status-info">
        <view class="status-title">连续签到</view>
        <view class="status-number">{{ continuousDays }}</view>
        <view class="status-unit">天</view>
      </view>
      <view class="today-status" @click="handleSign">
        <view class="sign-button" :class="{ 'signed': isSignedToday, 'loading': loading }">
          <text class="sign-text" v-if="!loading">{{ isSignedToday ? '已签到' : '签到' }}</text>
          <text class="sign-text" v-else>签到中...</text>
          <text class="sign-icon" v-if="isSignedToday && !loading">✓</text>
        </view>
      </view>
    </view>

    <!-- 最近30天日历 -->
    <view class="calendar-section">
      <view class="section-title">最近30天签到记录</view>
      <view class="calendar-container">
        <!-- 周标题 -->
        <view class="week-header">
          <view class="week-day" v-for="day in weekDays" :key="day">{{ day }}</view>
        </view>
        <!-- 日历网格 -->
        <view class="calendar-grid">
          <view
            class="calendar-day"
            v-for="(day, index) in calendarDays"
            :key="index"
            :class="{
              'signed': day.signed,
              'today': day.isToday,
              'future': day.isFuture
            }"
          >
            <view class="day-number">{{ day.day }}</view>
            <view class="day-status" v-if="day.signed">✓</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 签到记录 -->
    <view class="record-section">
      <view class="section-title">签到记录</view>
      <view class="record-list" v-if="signRecords.length > 0">
        <view
          class="record-item"
          v-for="(record, index) in signRecords"
          :key="index"
        >
          <view class="record-date">{{ record.date }}</view>
          <view class="record-status">{{ record.status }}</view>
          <view class="record-time">{{ record.time }}</view>
        </view>
      </view>
      <view class="empty-state" v-else-if="!loading">
        <view class="empty-icon">📅</view>
        <view class="empty-text">暂无签到记录</view>
        <view class="empty-tip">开始您的第一次签到吧！</view>
      </view>
      <view class="loading-state" v-else>
        <view class="loading-text">加载中...</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SignIndex',
  data() {
    return {
      // 签到状态
      isSignedToday: false,
      continuousDays: 0,

      // 周标题
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],

      // 最近30天日历数据
      calendarDays: [],

      // 签到记录
      signRecords: [],

      // 签到历史数据（用于日历显示）
      signHistoryMap: new Map(),

      // 加载状态
      loading: false
    }
  },

  onLoad() {
    this.initSignData()
  },

  onShow() {
    // 每次显示页面时刷新签到状态
    this.checkTodaySignStatus()
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 初始化签到数据
     */
    async initSignData() {
      try {
        this.loading = true

        // 并行请求今日签到状态和签到历史
        const [todayStatus, signHistory] = await Promise.all([
          this.checkTodaySignStatus(),
          this.getSignHistory()
        ])

        // 生成日历数据
        this.generateCalendarDays()

      } catch (error) {
        console.error('初始化签到数据失败:', error)
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    /**
     * 检查今天是否已签到
     */
    async checkTodaySignStatus() {
      try {
        const response = await this.$ajax.get('/user/checkTodaySign', {}, false)

        if (response && response.code === 200) {
          this.isSignedToday = response.data.isSigned || false
          this.continuousDays = response.data.continuousDays || 0
          return response.data
        } else {
          throw new Error(response?.msg || '检查签到状态失败')
        }
      } catch (error) {
        console.error('检查今日签到状态失败:', error)
        // 不显示错误提示，保持静默
        return null
      }
    },

    /**
     * 获取30天内签到历史
     */
    async getSignHistory() {
      try {
        const response = await this.$ajax.get('/user/getSignHistory')

        if (response && response.code === 200) {
          const historyData = response.data || []

          // 处理签到历史数据
          this.processSignHistory(historyData)

          return historyData
        } else {
          throw new Error(response?.msg || '获取签到历史失败')
        }
      } catch (error) {
        console.error('获取签到历史失败:', error)
        return []
      }
    },

    /**
     * 处理签到历史数据
     */
    processSignHistory(historyData) {
      // 清空现有数据
      this.signRecords = []
      this.signHistoryMap.clear()

      // 处理历史数据
      historyData.forEach(item => {
        // 使用 signTime 来获取实际的签到时间
        const signTime = new Date(item.signTime * 1000)

        // 获取签到日期（年-月-日格式）
        const dateStr = signTime.toISOString().split('T')[0]
        const timeStr = `${String(signTime.getHours()).padStart(2, '0')}:${String(signTime.getMinutes()).padStart(2, '0')}`

        // 添加到签到记录列表（用于显示）
        this.signRecords.push({
          date: dateStr,
          status: '已签到',
          time: timeStr,
          continuousDays: item.continuousDays
        })

        // 添加到签到历史映射（用于日历显示）
        this.signHistoryMap.set(dateStr, {
          signed: true,
          signTime: item.signTime,
          continuousDays: item.continuousDays
        })
      })

      // 按日期倒序排列签到记录
      this.signRecords.sort((a, b) => new Date(b.date) - new Date(a.date))
    },

    /**
     * 生成最近30天的日历数据
     */
    generateCalendarDays() {
      const days = []
      const today = new Date()

      // 生成最近30天的数据
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(today.getDate() - i)

        const dateStr = date.toISOString().split('T')[0]
        const signInfo = this.signHistoryMap.get(dateStr)

        const dayData = {
          day: date.getDate(),
          date: dateStr,
          signed: signInfo ? signInfo.signed : false,
          isToday: i === 0,
          isFuture: false
        }

        days.push(dayData)
      }

      this.calendarDays = days
    },

    /**
     * 处理签到点击
     */
    handleSign() {
      if (this.loading) {
        return
      }

      if (this.isSignedToday) {
        uni.showToast({
          title: '今日已签到',
          icon: 'none'
        })
        return
      }

      // 执行签到
      this.performSign()
    },

    /**
     * 执行签到操作
     */
    async performSign() {
      try {
        this.loading = true

        const response = await this.$api.post('/user/doSign')

        if (response && response.code === 200) {
          const { success, msg, continuousDays } = response.data

          if (success) {
            // 更新本地状态
            this.isSignedToday = true
            this.continuousDays = continuousDays || this.continuousDays + 1

            // 添加今日签到记录
            const now = new Date()
            const timeStr = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`
            const dateStr = now.toISOString().split('T')[0]

            // 添加到签到记录列表
            this.signRecords.unshift({
              date: dateStr,
              status: '已签到',
              time: timeStr,
              continuousDays: this.continuousDays
            })

            // 添加到签到历史映射
            this.signHistoryMap.set(dateStr, {
              signed: true,
              signTime: Math.floor(now.getTime() / 1000),
              continuousDays: this.continuousDays
            })

            // 更新日历显示
            this.generateCalendarDays()

            uni.showToast({
              title: msg || '签到成功！',
              icon: 'success'
            })
          } else {
            uni.showToast({
              title: msg || '签到失败',
              icon: 'none'
            })
          }
        } else {
          throw new Error(response?.msg || '签到请求失败')
        }
      } catch (error) {
        console.error('签到失败:', error)

        // 处理特定错误信息
        let errorMsg = '签到失败，请重试'
        if (error.message && error.message.includes('已经签到')) {
          errorMsg = '今天已经签到过了'
          // 刷新签到状态
          this.checkTodaySignStatus()
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-page {
  min-height: calc(100vh - 100rpx);
  background: #fff;
  padding-bottom: 40rpx;
}

/* 连签统计卡片 */
.sign-status-card {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  margin: 20rpx 30rpx;
  padding: 40rpx;
  border-radius: 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(30, 60, 114, 0.3);
  position: relative;
  overflow: hidden;
}

.sign-status-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.status-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  z-index: 1;
}

.status-title {
  font-size: 28rpx;
  color: #fff;
  opacity: 0.9;
}

.status-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.status-unit {
  font-size: 24rpx;
  color: #fff;
  opacity: 0.9;
}

.today-status {
  z-index: 1;
}

.sign-button {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  padding: 16rpx 32rpx;
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.sign-button.signed {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
}

.sign-button.loading {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  opacity: 0.7;
}

.sign-text {
  color: #fff;
  font-size: 26rpx;
  font-weight: 600;
}

.sign-icon {
  color: #fff;
  font-size: 20rpx;
}

/* 日历区域 */
.calendar-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.calendar-container {
  padding: 20rpx;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx 0;
}

.week-day {
  text-align: center;
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
  padding: 8rpx 0;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: #f8f9fa;
  position: relative;
  min-height: 80rpx;
  transition: all 0.3s ease;
}

.calendar-day.signed {
  background: #e8f5e8;
  border: 2rpx solid #4caf50;
}

.calendar-day.today {
  background: #1e3c72;
  color: #fff;
}

.calendar-day.today.signed {
  background: #4caf50;
  border: 2rpx solid #388e3c;
}

.calendar-day.future {
  background: #f0f0f0;
  opacity: 0.5;
}

.day-number {
  font-size: 24rpx;
  color: #333;
  font-weight: 600;
}

.calendar-day.today .day-number {
  color: #fff;
}

.calendar-day.signed .day-number {
  color: #2e7d32;
}

.calendar-day.today.signed .day-number {
  color: #fff;
}

.day-status {
  font-size: 18rpx;
  color: #4caf50;
  margin-top: 4rpx;
  font-weight: bold;
}

.calendar-day.today .day-status {
  color: #fff;
}



/* 通用区域样式 */
.record-section {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  background: #f8f9fa;
  margin: 0;
}

/* 签到记录 */
.record-list {
  padding: 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.record-item:last-child {
  border-bottom: none;
}

.record-date {
  font-size: 28rpx;
  color: #333;
  width: 200rpx;
  font-weight: 500;
}

.record-status {
  flex: 1;
  font-size: 26rpx;
  color: #4caf50;
  text-align: center;
  font-weight: 500;
}

.record-time {
  font-size: 26rpx;
  color: #666;
  width: 120rpx;
  text-align: right;
  font-weight: 400;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 24rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}
</style>
